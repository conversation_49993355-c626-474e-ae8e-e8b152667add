<?php
/**
 * My Courses Page for Market Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Tutor LMS fonksiyonlarının mevcut olup olmadığını kontrol et
if (!function_exists('tutor') || !function_exists('tutor_utils')) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Tutor LMS eklentisi aktif değil. Kursları yönetmek için Tutor LMS eklentisini aktifleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Tutor LMS Input class'ı<PERSON><PERSON> kull<PERSON>
use TUTOR\Input;
use Tutor\Models\CourseModel;

// Get the user ID and active tab.
$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

$active_tab = 'my-courses';
$page = isset($_GET['pagenr']) ? $_GET['pagenr'] : '';
if ($page) {
    switch ($page) {
        case 'draft-courses':
            $active_tab = 'draft-courses';
            break;
        case 'pending-courses':
            $active_tab = 'pending-courses';
            break;
        case 'schedule-courses':
            $active_tab = 'schedule-courses';
            break;
        default:
            $active_tab = 'my-courses';
            break;
    }
}

// Map required course status according to page.
$status_map = array(
    'my-courses'        => CourseModel::STATUS_PUBLISH,
    'draft-courses'     => CourseModel::STATUS_DRAFT,
    'pending-courses'   => CourseModel::STATUS_PENDING,
    'schedule-courses'  => CourseModel::STATUS_FUTURE,
);

// Set currently required course status for current tab.
$status = isset($status_map[$active_tab]) ? $status_map[$active_tab] : CourseModel::STATUS_PUBLISH;
$post_type = apply_filters('tutor_dashboard_course_list_post_type', array(tutor()->course_post_type));

// Get counts for course tabs.
$count_map = array(
    'publish' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PUBLISH, 0, 0, true, $post_type),
    'pending' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PENDING, 0, 0, true, $post_type),
    'draft'   => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_DRAFT, 0, 0, true, $post_type),
    'future'  => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_FUTURE, 0, 0, true, $post_type),
);

$course_archive_arg = isset($GLOBALS['tutor_course_archive_arg']) ? $GLOBALS['tutor_course_archive_arg']['column_per_row'] : null;
$courseCols = null === $course_archive_arg ? tutor_utils()->get_option('courses_col_per_row', 4) : $course_archive_arg;
$per_page = tutor_utils()->get_option('courses_per_page', 10);
$paged = Input::get('current_page', 1, Input::TYPE_INT);
$offset = $per_page * ($paged - 1);
$results = CourseModel::get_courses_by_instructor($current_user_id, $status, $offset, $per_page, false, $post_type);
$show_course_delete = true;

$tabs = array(
    'publish' => array(
        'title' => __('Yayınlanan', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'my-courses',
    ),
    'pending' => array(
        'title' => __('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'pending-courses',
    ),
    'draft'   => array(
        'title' => __('Taslak', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'draft-courses',
    ),
    'future'  => array(
        'title' => __('Programlı', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'schedule-courses',
    ),
);

if (!current_user_can('administrator') && !tutor_utils()->get_option('instructor_can_delete_course')) {
    $show_course_delete = false;
}

$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));
?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php esc_html_e('Kurslarım', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php esc_html_e('Kurslarınızı yönetin ve düzenleyin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <?php if (current_user_can(tutor()->instructor_role)) : ?>
                                <a href="<?php echo esc_url($dashboard_url . 'create-course'); ?>" class="btn btn-primary">
                                    <em class="icon ni ni-plus"></em>
                                    <span><?php esc_html_e('Yeni Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner">
                                <div class="nk-tb-list nk-tb-ulist">
                                    <div class="nk-tb-item nk-tb-head">
                                        <div class="nk-tb-col">
                                            <ul class="nav nav-tabs nav-tabs-s2">
                                                <?php foreach ($tabs as $key => $tab) : ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link<?php echo esc_attr($tab['link'] === $active_tab ? ' active' : ''); ?>" 
                                                           href="<?php echo esc_url($dashboard_url . $tab['link']); ?>">
                                                            <?php echo esc_html($tab['title']); ?> 
                                                            <span class="badge badge-sm badge-outline-secondary ml-1">
                                                                <?php echo esc_html($count_map[$key]); ?>
                                                            </span>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course list -->
                <div class="nk-block">
                    <?php
                    $placeholder_img = tutor()->url . 'assets/images/placeholder.svg';

                    if (!is_array($results) || (!count($results) && 1 == $paged)) {
                        ?>
                        <div class="card card-bordered">
                            <div class="card-inner text-center">
                                <div class="nk-empty-state">
                                    <div class="nk-empty-state-icon">
                                        <em class="icon ni ni-book"></em>
                                    </div>
                                    <h4 class="nk-empty-state-title"><?php esc_html_e('Henüz kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                    <p class="nk-empty-state-text"><?php esc_html_e('İlk kursunuzu oluşturmak için "Yeni Kurs" butonuna tıklayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                    <?php if (current_user_can(tutor()->instructor_role)) : ?>
                                        <div class="nk-empty-state-action">
                                            <a href="<?php echo esc_url($dashboard_url . 'create-course'); ?>" class="btn btn-primary">
                                                <em class="icon ni ni-plus"></em>
                                                <span><?php esc_html_e('Yeni Kurs Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    } else {
                        ?>
                        <div class="row g-gs">
                            <?php
                            foreach ($results as $course) {
                                $course_id = $course->ID;
                                $tutor_course_img = get_tutor_course_thumbnail_src($course_id);
                                $course_url = get_the_permalink($course_id);
                                $edit_url = tutor_utils()->course_edit_link($course_id);
                                $course_status = get_post_status($course_id);

                                // Tutor eklentisinden ek bilgiler
                                $course_students = tutor_utils()->count_enrolled_users_by_course($course_id);
                                $course_date = get_the_date('', $course_id);
                                $course_time = get_the_time('', $course_id);
                                
                                // Status badge
                                $status_badge = '';
                                switch ($course_status) {
                                    case 'publish':
                                        $status_badge = '<span class="badge badge-success">' . esc_html__('Yayınlandı', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                        break;
                                    case 'pending':
                                        $status_badge = '<span class="badge badge-warning">' . esc_html__('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                        break;
                                    case 'draft':
                                        $status_badge = '<span class="badge badge-secondary">' . esc_html__('Taslak', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                        break;
                                    case 'future':
                                        $status_badge = '<span class="badge badge-info">' . esc_html__('Programlı', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                        break;
                                }
                                ?>
                                <div class="col-sm-6 col-lg-4 col-xxl-3">
                                    <div id="tutor-dashboard-my-course-<?php echo esc_attr($course_id); ?>" class="card card-bordered product-card h-100">
                                        <div class="product-thumb">
                                            <a href="<?php echo esc_url($course_url); ?>">
                                                <img src="<?php echo empty($tutor_course_img) ? esc_url($placeholder_img) : esc_url($tutor_course_img); ?>"
                                                     class="card-img-top" alt="<?php echo esc_attr(get_the_title($course_id)); ?>" loading="lazy">
                                            </a>
                                            <ul class="product-badges">
                                                <li><?php echo wp_kses_post($status_badge); ?></li>
                                            </ul>
                                        </div>
                                        <div class="card-inner text-center">
                                            <h5 class="product-title">
                                                <a href="<?php echo esc_url($course_url); ?>"><?php echo esc_html(get_the_title($course_id)); ?></a>
                                            </h5>

                                            <!-- Kurs Meta Bilgileri -->
                                            <div class="course-meta mb-3">
                                                <div class="row text-muted small">
                                                    <div class="col-12 mb-2">
                                                        <i class="icon ni ni-calendar"></i>
                                                        <span><?php echo esc_html($course_date); ?> <?php echo esc_html($course_time); ?></span>
                                                    </div>
                                                    <div class="col-12 mb-2">
                                                        <i class="icon ni ni-users"></i>
                                                        <span><?php echo esc_html($course_students); ?> <?php esc_html_e('Kayıtlı Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="product-price text-primary h5">
                                                <?php
                                                $course_price = tutor_utils()->get_course_price($course_id);
                                                if ($course_price) {
                                                    echo wp_kses_post($course_price);
                                                } else {
                                                    esc_html_e('Ücretsiz', 'marketking-multivendor-marketplace-for-woocommerce');
                                                }
                                                ?>
                                            </div>
                                            <div class="product-action">
                                                <a href="<?php echo esc_url($edit_url); ?>" class="btn btn-sm btn-outline-primary">
                                                    <em class="icon ni ni-edit"></em>
                                                    <span><?php esc_html_e('Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                </a>
                                                <a href="<?php echo esc_url($course_url); ?>" class="btn btn-sm btn-outline-secondary">
                                                    <em class="icon ni ni-eye"></em>
                                                    <span><?php esc_html_e('Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                        
                        <?php
                        // Pagination
                        $total_courses = CourseModel::get_courses_by_instructor($current_user_id, $status, 0, 0, true, $post_type);
                        if ($total_courses > $per_page) {
                            $total_pages = ceil($total_courses / $per_page);
                            if ($total_pages > 1) {
                                ?>
                                <div class="card card-bordered mt-4">
                                    <div class="card-inner">
                                        <div class="nk-block-between-md g-3">
                                            <div class="g">
                                                <ul class="pagination justify-content-center justify-content-md-start">
                                                    <?php
                                                    $current_url = $dashboard_url . $active_tab;
                                                    
                                                    // Previous page
                                                    if ($paged > 1) {
                                                        $prev_page = $paged - 1;
                                                        $prev_url = $prev_page > 1 ? add_query_arg('current_page', $prev_page, $current_url) : $current_url;
                                                        ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?php echo esc_url($prev_url); ?>">
                                                                <em class="icon ni ni-chevrons-left"></em>
                                                            </a>
                                                        </li>
                                                        <?php
                                                    }
                                                    
                                                    // Page numbers
                                                    for ($i = 1; $i <= $total_pages; $i++) {
                                                        $page_url = $i > 1 ? add_query_arg('current_page', $i, $current_url) : $current_url;
                                                        ?>
                                                        <li class="page-item<?php echo $i == $paged ? ' active' : ''; ?>">
                                                            <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo esc_html($i); ?></a>
                                                        </li>
                                                        <?php
                                                    }
                                                    
                                                    // Next page
                                                    if ($paged < $total_pages) {
                                                        $next_page = $paged + 1;
                                                        $next_url = add_query_arg('current_page', $next_page, $current_url);
                                                        ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?php echo esc_url($next_url); ?>">
                                                                <em class="icon ni ni-chevrons-right"></em>
                                                            </a>
                                                        </li>
                                                        <?php
                                                    }
                                                    ?>
                                                </ul>
                                            </div>
                                            <div class="g">
                                                <div class="pagination-goto d-flex justify-content-center justify-content-md-end gx-3">
                                                    <div><?php printf(esc_html__('Sayfa %1$d / %2$d', 'marketking-multivendor-marketplace-for-woocommerce'), $paged, $total_pages); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
